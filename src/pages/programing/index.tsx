import { useState } from 'react';
import { createProgrammingColumns } from './components/table/columns/programming-columns';
import { DeleteProgrammingModal } from './components/modals/delete-programming-modal';
import { ViewProgrammingModal } from './components/modals/view-programming-modal';

import { useDeleteProgramming } from './hooks/delete-programming.hook';
import { useDeleteMultipleProgramming } from './hooks/delete-multiple-programming.hook';
import { useFindAllProgramming } from './hooks/find-all-programming.hook';
import { IProgramming, IProgrammingFindAllResponse } from './api/requests/find-all';
import { ProgrammingDataTable } from './components/table/programming-data-table';

const initialProgrammingData: IProgrammingFindAllResponse = {
	data: [] as IProgramming[],
	currentPage: 1,
	pageSize: 10,
	totalCount: 0,
	totalPages: 1,
};

export function ProgrammingPage() {
	const { data, isLoading } = useFindAllProgramming();
	const [deleteModalOpen, setDeleteModalOpen] = useState(false);
	const [viewModalOpen, setViewModalOpen] = useState(false);
	const [programmingToDelete, setProgrammingToDelete] = useState<Array<{ id: string; name: string }>>([]);
	const [programmingToView, setProgrammingToView] = useState<IProgramming | null>(null);
	const { deleteProgramming, isLoading: isDeleting } = useDeleteProgramming();
	const { deleteMultipleProgramming, isLoading: isDeletingMultiple } = useDeleteMultipleProgramming();

	const handleDeleteProgramming = async (id: string) => {
		if (!data?.success) return;
		const programmingIds = id.split(',');

		if (programmingIds.length > 1) {
			const currentProgramming = data.data as IProgrammingFindAllResponse;

			const programmingToDelete = programmingIds
				.map((programmingId) => {
					const programming = currentProgramming.data.find((p: IProgramming) => p.id === programmingId);
					return programming ? { id: programmingId, name: `Programação ${programming.id}` } : null;
				})
				.filter((programming): programming is { id: string; name: string } => programming !== null);

			if (programmingToDelete.length > 0) {
				setProgrammingToDelete(programmingToDelete);
				setDeleteModalOpen(true);
			}
			return;
		}

		try {
			await deleteProgramming(id);
		} catch (error) {
			console.error('Erro ao excluir programação:', error);
		}
	};

	const handleConfirmDelete = async () => {
		try {
			const programmingIds = programmingToDelete.map((p) => p.id);
			await deleteMultipleProgramming(programmingIds);
			setDeleteModalOpen(false);
			setProgrammingToDelete([]);
		} catch (error) {
			console.error('Erro ao excluir programações:', error);
		}
	};

	const handleCloseDeleteModal = () => {
		setDeleteModalOpen(false);
		setProgrammingToDelete([]);
	};

	const handleViewProgramming = (id: string) => {
		if (!data?.success) return;
		const currentProgramming = data.data as IProgrammingFindAllResponse;
		const programming = currentProgramming.data.find((p: IProgramming) => p.id === id);

		if (programming) {
			setProgrammingToView(programming);
			setViewModalOpen(true);
		}
	};

	const handleCloseViewModal = () => {
		setViewModalOpen(false);
		setProgrammingToView(null);
	};

	const columns = createProgrammingColumns({
		onDelete: handleDeleteProgramming,
		onEdit: undefined,
		onView: handleViewProgramming,
		onDuplicate: undefined,
	});

	return (
		<>
			<ProgrammingDataTable
				columns={columns}
				data={data?.success ? data.data : initialProgrammingData}
				isLoading={isLoading}
				messageError={!data?.success ? data?.data.message : undefined}
			/>
			<ViewProgrammingModal isOpen={viewModalOpen} onClose={handleCloseViewModal} programming={programmingToView} backdrop="blur" />
			<DeleteProgrammingModal
				isOpen={deleteModalOpen}
				onClose={handleCloseDeleteModal}
				onConfirm={handleConfirmDelete}
				programmingName={programmingToDelete.map((p) => p.name).join(', ')}
				isLoading={isDeleting || isDeletingMultiple}
			/>
		</>
	);
}

export default ProgrammingPage;
